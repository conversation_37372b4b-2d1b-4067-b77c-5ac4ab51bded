/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : config.h
  * @brief          : 统一配置文件
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __CONFIG_H
#define __CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* 第一个舵机控制配置(180度舵机) */
#define SERVO_PWM_PERIOD    20000     // PWM周期20ms (20000us)
#define SERVO_MIN_PULSE     500       // 最小脉宽0.5ms对应-90度
#define SERVO_MAX_PULSE     2500      // 最大脉宽2.5ms对应+90度
#define SERVO_MID_PULSE     1500      // 中位脉宽1.5ms对应0度
#define SERVO_MIN_ANGLE     -90       // 最小角度
#define SERVO_MAX_ANGLE     90        // 最大角度

/* 第二个舵机控制配置(270度舵机) */
#define SERVO2_MIN_PULSE    500       // 最小脉宽0.5ms对应0度
#define SERVO2_MAX_PULSE    2500      // 最大脉宽2.5ms对应270度
#define SERVO2_MID_PULSE    1500      // 中位脉宽1.5ms对应135度
#define SERVO2_MIN_ANGLE    0         // 最小角度0度
#define SERVO2_MAX_ANGLE    270       // 最大角度270度

/* 定时器配置 */
#define TIM_PRESCALER       8         // 预分频器(8MHz/8=1MHz,1us分辨率)
#define TIM_PERIOD          (SERVO_PWM_PERIOD-1)  // 定时器周期

/* 系统配置 */
#define SYSTEM_CLOCK_MHZ    8         // 系统时钟频率8MHz
#define DELAY_MS            1000      // 测试延时时间

/* UART通信配置 */
#define UART_RX_BUFFER_SIZE 256       // UART接收缓冲区大小



#ifdef __cplusplus
}
#endif

#endif /* __CONFIG_H */
