/* 
 * STM32F103C8T6上位机串口通信模块
 * 用于与MaixCAM下位机通信
 */

#ifndef __UART_COMM_H
#define __UART_COMM_H

#include "main.h"
#include "usart.h"
#include "config.h"  // 包含统一配置文件
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
/* 定义激光点和矩形角点结构体 */
typedef struct {
    int16_t x;
    int16_t y;
} Point_t;

typedef struct {
    Point_t points[4];  // 按照顺时针顺序存储4个角点
    uint8_t valid;      // 标记数据是否有效
} Rectangle_t;

/* 函数声明 */
void UART_Init(void);
void UART_SendCommand(const char* cmd);
void UART_RequestRectangleCoordinates(void);
void UART_Handler(void);
void UART_ProcessReceivedData(void);
void UART_ClearLaserPoints(void);

/* 获取处理后的数据 */
Rectangle_t* UART_GetInnerRectangle(void);
Rectangle_t* UART_GetOuterRectangle(void);
Point_t* UART_GetRedLaserPoints(uint8_t* count);
Point_t* UART_GetGreenLaserPoints(uint8_t* count);

#endif /* __UART_COMM_H */

