/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "config.h"
#include "uart_comm.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
uint32_t lastRequestTime = 0;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
	
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */
UART_Init();
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_TIM3_Init();
  MX_TIM4_Init();
  MX_TIM2_Init();
  MX_USART1_UART_Init();
  /* USER CODE BEGIN 2 */
  HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1); // 启动第一个舵机PWM输出
  HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_1); // 启动第二个舵机PWM输出
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
		 HAL_GPIO_WritePin(GPIOC,GPIO_PIN_13,GPIO_PIN_RESET);
		/* 处理串口数据 */
    UART_Handler();
    
    /* 每隔2秒请求一次矩形坐标 */
    uint32_t currentTime = HAL_GetTick();
    if (currentTime - lastRequestTime > 2000) {
        UART_RequestRectangleCoordinates();
        lastRequestTime = currentTime;
    }
    
    /* 获取并处理接收到的数据 */
    Rectangle_t* innerRect = UART_GetInnerRectangle();
    Rectangle_t* outerRect = UART_GetOuterRectangle();
    
    /* 如果接收到了有效的矩形坐标 */
    if (innerRect->valid && outerRect->valid) {
        /* 在这里添加应用代码，处理矩形坐标信息 */
        /* 例如：可以计算矩形尺寸、方向等 */
    }
    
    /* 获取激光点数据 */
    uint8_t redCount, greenCount;
    Point_t* redPoints = UART_GetRedLaserPoints(&redCount);
    Point_t* greenPoints = UART_GetGreenLaserPoints(&greenCount);
    
    /* 处理激光点数据 */
    if (redCount > 0 || greenCount > 0) {
        /* 在这里添加应用代码，处理激光点信息 */
        /* 例如：计算激光点相对于矩形框的位置 */
    }
		int8_t y=0;
		int8_t x=0;
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 双舵机测试(180度+270度舵机)
    Servo_SetAngle(0+x);     // 第一个舵机转到中位(0度)
    Servo2_SetAngle(18+y);  // 第二个舵机转到中位(135度)
    HAL_Delay(DELAY_MS);
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/**
 * @brief 设置第一个舵机角度
 * @param angle: 角度值(-90到+90度)
 */
void Servo_SetAngle(int16_t angle) {
  // 限制角度范围
  if(angle < SERVO_MIN_ANGLE) angle = SERVO_MIN_ANGLE;
  if(angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;

  // 角度转换为脉宽: -90度=0.5ms, 0度=1.5ms, +90度=2.5ms
  uint16_t pulse_width = SERVO_MID_PULSE + (angle * (SERVO_MAX_PULSE - SERVO_MIN_PULSE) / 180);

  // 设置PWM占空比
  __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pulse_width);
}

/**
 * @brief 设置第二个舵机角度(270度舵机)
 * @param angle: 角度值(0到270度)
 */
void Servo2_SetAngle(int16_t angle) {
  // 限制角度范围
  if(angle < SERVO2_MIN_ANGLE) angle = SERVO2_MIN_ANGLE;
  if(angle > SERVO2_MAX_ANGLE) angle = SERVO2_MAX_ANGLE;

  // 角度转换为脉宽: 0度=0.5ms, 135度=1.5ms, 270度=2.5ms
  uint16_t pulse_width = SERVO2_MIN_PULSE + (angle * (SERVO2_MAX_PULSE - SERVO2_MIN_PULSE) / 270);

  // 设置PWM占空比
  __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_1, pulse_width);
}

/**
 * @brief 直接设置第一个舵机脉宽
 * @param pulse_us: 脉宽值(微秒)
 */
void Servo_SetPulse(uint16_t pulse_us) {
  // 限制脉宽范围
  if(pulse_us < SERVO_MIN_PULSE) pulse_us = SERVO_MIN_PULSE;
  if(pulse_us > SERVO_MAX_PULSE) pulse_us = SERVO_MAX_PULSE;

  // 设置PWM占空比
  __HAL_TIM_SET_COMPARE(&htim3, TIM_CHANNEL_1, pulse_us);
}

/**
 * @brief 直接设置第二个舵机脉宽(270度舵机)
 * @param pulse_us: 脉宽值(微秒)
 */
void Servo2_SetPulse(uint16_t pulse_us) {
  // 限制脉宽范围
  if(pulse_us < SERVO2_MIN_PULSE) pulse_us = SERVO2_MIN_PULSE;
  if(pulse_us > SERVO2_MAX_PULSE) pulse_us = SERVO2_MAX_PULSE;

  // 设置PWM占空比
  __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_1, pulse_us);
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
