#include "uart_comm.h"

/* 定义串口接收缓冲区 */
#define UART_RX_BUFFER_SIZE 256
static uint8_t uartRxBuffer[UART_RX_BUFFER_SIZE];
static volatile uint16_t uartRxIndex = 0;
static volatile uint8_t uartRxComplete = 0;

/* 全局变量 */
static Rectangle_t innerRect = {0};
static Rectangle_t outerRect = {0};
static Point_t redLaserPoints[10] = {0};  // 最多存储10个红色激光点
static Point_t greenLaserPoints[10] = {0}; // 最多存储10个绿色激光点
static uint8_t redLaserCount = 0;
static uint8_t greenLaserCount = 0;

/* 函数声明 */
static void UART_ParseInnerRectangle(const char* data);
static void UART_ParseOuterRectangle(const char* data);
static void UART_ParseLaserPoint(const char* data, uint8_t isRed);

/**
 * @brief 初始化串口
 */
void UART_Init(void)
{
    /* 启用串口接收中断 */
    HAL_UART_Receive_IT(&huart1, &uartRxBuffer[uartRxIndex], 1);
    
    /* 重定向printf到串口 */
    #ifdef DEBUG_PRINT
    printf("串口初始化完成，波特率: 115200\r\n");
    #endif
}

/**
 * @brief 发送命令到下位机
 * @param cmd 要发送的命令字符串
 */
void UART_SendCommand(const char* cmd)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)cmd, strlen(cmd), 100);
    #ifdef DEBUG_PRINT
    printf("发送命令: %s\r\n", cmd);
    #endif
}

/**
 * @brief 请求下位机发送矩形角点坐标
 */
void UART_RequestRectangleCoordinates(void)
{
    UART_SendCommand("JX");
}

/**
 * @brief 处理接收到的数据
 */
void UART_ProcessReceivedData(void)
{
    if (uartRxComplete)
    {
        /* 复制接收到的数据以便处理 */
        char rxData[UART_RX_BUFFER_SIZE];
        strcpy(rxData, (char*)uartRxBuffer);
       
        /* 根据数据前缀判断数据类型并解析 */
        if (strncmp(rxData, "JXI", 3) == 0)
        {
            /* 内框角点坐标 */
            UART_ParseInnerRectangle(rxData);
        }
        else if (strncmp(rxData, "JXO", 3) == 0)
        {
            /* 外框角点坐标 */
            UART_ParseOuterRectangle(rxData);
        }
        else if (strncmp(rxData, "RD", 2) == 0)
        {
            /* 红色激光点坐标 */
            UART_ParseLaserPoint(rxData, 1);
        }
        else if (strncmp(rxData, "GD", 2) == 0)
        {
            /* 绿色激光点坐标 */
            UART_ParseLaserPoint(rxData, 0);
        }
        
        /* 重置接收状态 */
        uartRxIndex = 0;
        uartRxComplete = 0;
        
        /* 重新启动接收 */
        HAL_UART_Receive_IT(&huart1, &uartRxBuffer[uartRxIndex], 1);
    }
}

/**
 * @brief 解析内框角点坐标
 * @param data 格式为"JXIax{x1}y{y1}bx{x2}y{y2}cx{x3}y{y3}dx{x4}y{y4}"的字符串
 */
static void UART_ParseInnerRectangle(const char* data)
{
    int x1, y1, x2, y2, x3, y3, x4, y4;
    int result = sscanf(data, "JXIax%dy%dbx%dy%dcx%dy%ddx%dy%d", 
                        &x1, &y1, &x2, &y2, &x3, &y3, &x4, &y4);
    
    if (result == 8) {
        innerRect.points[0].x = x1;
        innerRect.points[0].y = y1;
        innerRect.points[1].x = x2;
        innerRect.points[1].y = y2;
        innerRect.points[2].x = x3;
        innerRect.points[2].y = y3;
        innerRect.points[3].x = x4;
        innerRect.points[3].y = y4;
        innerRect.valid = 1;
        
        #ifdef DEBUG_PRINT
        printf("内框: (%d,%d),(%d,%d),(%d,%d),(%d,%d)\r\n",
               x1, y1, x2, y2, x3, y3, x4, y4);
        #endif
    }
}

/**
 * @brief 解析外框角点坐标
 * @param data 格式为"JXOax{x1}y{y1}bx{x2}y{y2}cx{x3}y{y3}dx{x4}y{y4}"的字符串
 */
static void UART_ParseOuterRectangle(const char* data)
{
    int x1, y1, x2, y2, x3, y3, x4, y4;
    int result = sscanf(data, "JXOax%dy%dbx%dy%dcx%dy%ddx%dy%d", 
                        &x1, &y1, &x2, &y2, &x3, &y3, &x4, &y4);
    
    if (result == 8) {
        outerRect.points[0].x = x1;
        outerRect.points[0].y = y1;
        outerRect.points[1].x = x2;
        outerRect.points[1].y = y2;
        outerRect.points[2].x = x3;
        outerRect.points[2].y = y3;
        outerRect.points[3].x = x4;
        outerRect.points[3].y = y4;
        outerRect.valid = 1;
        
        #ifdef DEBUG_PRINT
        printf("外框: (%d,%d),(%d,%d),(%d,%d),(%d,%d)\r\n",
               x1, y1, x2, y2, x3, y3, x4, y4);
        #endif
    }
}

/**
 * @brief 解析激光点坐标
 * @param data 格式为"RDx{x}y{y}"或"GDx{x}y{y}"的字符串
 * @param isRed 1表示红色激光点，0表示绿色激光点
 */
static void UART_ParseLaserPoint(const char* data, uint8_t isRed)
{
    int x, y;
    int result = sscanf(data + 2, "x%dy%d", &x, &y);
    
    if (result == 2) {
        if (isRed) {
            if (redLaserCount < 10) {
                redLaserPoints[redLaserCount].x = x;
                redLaserPoints[redLaserCount].y = y;
                redLaserCount++;
                
                #ifdef DEBUG_PRINT
                printf("红激光: (%d,%d)\r\n", x, y);
                #endif
            }
        } else {
            if (greenLaserCount < 10) {
                greenLaserPoints[greenLaserCount].x = x;
                greenLaserPoints[greenLaserCount].y = y;
                greenLaserCount++;
                
                #ifdef DEBUG_PRINT
                printf("绿激光: (%d,%d)\r\n", x, y);
                #endif
            }
        }
    }
}

/**
 * @brief 清除激光点数据
 */
void UART_ClearLaserPoints(void)
{
    memset(redLaserPoints, 0, sizeof(redLaserPoints));
    memset(greenLaserPoints, 0, sizeof(greenLaserPoints));
    redLaserCount = 0;
    greenLaserCount = 0;
}

/**
 * @brief 主循环中调用的串口处理函数
 */
void UART_Handler(void)
{
    /* 处理接收到的数据 */
    UART_ProcessReceivedData();
}

/**
 * @brief 获取内框矩形数据
 * @return 指向内框矩形结构的指针
 */
Rectangle_t* UART_GetInnerRectangle(void)
{
    return &innerRect;
}

/**
 * @brief 获取外框矩形数据
 * @return 指向外框矩形结构的指针
 */
Rectangle_t* UART_GetOuterRectangle(void)
{
    return &outerRect;
}

/**
 * @brief 获取红色激光点数据
 * @param count 输出参数，返回红色激光点的数量
 * @return 指向红色激光点数组的指针
 */
Point_t* UART_GetRedLaserPoints(uint8_t* count)
{
    if (count)
        *count = redLaserCount;
    return redLaserPoints;
}

/**
 * @brief 获取绿色激光点数据
 * @param count 输出参数，返回绿色激光点的数量
 * @return 指向绿色激光点数组的指针
 */
Point_t* UART_GetGreenLaserPoints(uint8_t* count)
{
    if (count)
        *count = greenLaserCount;
    return greenLaserPoints;
}

/**
  * @brief  串口接收中断回调函数
  * @param  huart 串口句柄
  * @retval None
  */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    extern uint8_t uartRxBuffer[];
    extern volatile uint16_t uartRxIndex;
    extern volatile uint8_t uartRxComplete;
    
    if (huart->Instance == USART1)
    {
        /* 接收到一个字节数据 */
        uartRxIndex++;
        
        /* 检查是否接收到结束字符或缓冲区已满 */
        if (uartRxBuffer[uartRxIndex-1] == '\n' || 
            uartRxBuffer[uartRxIndex-1] == '\r' || 
            uartRxIndex >= UART_RX_BUFFER_SIZE - 1)
        {
            uartRxBuffer[uartRxIndex] = '\0';  /* 添加字符串结束符 */
            uartRxComplete = 1;
        }
        else
        {
            /* 继续接收下一个字节 */
            HAL_UART_Receive_IT(huart, &uartRxBuffer[uartRxIndex], 1);
        }
    }
}
