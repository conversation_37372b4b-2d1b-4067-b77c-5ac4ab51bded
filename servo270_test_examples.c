/**
  ******************************************************************************
  * @file    servo270_test_examples.c
  * @brief   270度舵机专用测试示例代码
  ******************************************************************************
  */

#include "main.h"
#include "config.h"

/**
 * @brief 270度舵机基本角度控制测试
 */
void Test_Servo270_BasicAngleControl(void) {
    // 测试270度舵机基本角度控制
    Servo2_SetAngle(135);   // 中位(135度)
    HAL_Delay(1000);
    
    Servo2_SetAngle(0);     // 最小角度(0度)
    HAL_Delay(1000);
    
    Servo2_SetAngle(270);   // 最大角度(270度)
    HAL_Delay(1000);
    
    Servo2_SetAngle(90);    // 1/4位置(90度)
    HAL_Delay(1000);
    
    Servo2_SetAngle(180);   // 1/2位置(180度)
    HAL_Delay(1000);
    
    Servo2_SetAngle(135);   // 回中位
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机连续扫描测试
 */
void Test_Servo270_ContinuousSweep(void) {
    // 从0度到270度扫描
    for(int16_t angle = 0; angle <= 270; angle += 15) {
        Servo2_SetAngle(angle);
        HAL_Delay(100);
    }
    
    // 从270度到0度扫描
    for(int16_t angle = 270; angle >= 0; angle -= 15) {
        Servo2_SetAngle(angle);
        HAL_Delay(100);
    }
    
    // 回到中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机精确脉宽控制测试
 */
void Test_Servo270_PrecisePulseControl(void) {
    // 使用精确脉宽控制
    Servo2_SetPulse(1500);  // 1.5ms中位(135度)
    HAL_Delay(1000);
    
    Servo2_SetPulse(500);   // 0.5ms(0度)
    HAL_Delay(1000);
    
    Servo2_SetPulse(2500);  // 2.5ms(270度)
    HAL_Delay(1000);
    
    Servo2_SetPulse(1000);  // 1.0ms(约67.5度)
    HAL_Delay(1000);
    
    Servo2_SetPulse(2000);  // 2.0ms(约202.5度)
    HAL_Delay(1000);
    
    Servo2_SetPulse(1500);  // 回中位
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机分段测试
 */
void Test_Servo270_SegmentTest(void) {
    // 分段测试270度舵机的各个区间
    uint16_t angles[] = {0, 45, 90, 135, 180, 225, 270};
    uint8_t segments = sizeof(angles) / sizeof(angles[0]);
    
    for(uint8_t i = 0; i < segments; i++) {
        Servo2_SetAngle(angles[i]);
        HAL_Delay(800);
    }
    
    // 回到中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机微调测试
 */
void Test_Servo270_FineTuning(void) {
    // 在中位附近微调(120-150度)
    for(int16_t angle = 120; angle <= 150; angle += 5) {
        Servo2_SetAngle(angle);
        HAL_Delay(200);
    }
    
    // 回到中位
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 180度+270度双舵机协调测试
 */
void Test_Mixed_ServoCoordination(void) {
    // 协调测试：180度舵机(-90到+90) + 270度舵机(0到270)
    
    // 测试1：两个舵机都在中位
    Servo_SetAngle(0);      // 180度舵机中位
    Servo2_SetAngle(135);   // 270度舵机中位
    HAL_Delay(1000);
    
    // 测试2：180度舵机左极限，270度舵机0度
    Servo_SetAngle(-90);    // 180度舵机左极限
    Servo2_SetAngle(0);     // 270度舵机0度
    HAL_Delay(1000);
    
    // 测试3：180度舵机右极限，270度舵机270度
    Servo_SetAngle(90);     // 180度舵机右极限
    Servo2_SetAngle(270);   // 270度舵机270度
    HAL_Delay(1000);
    
    // 测试4：交叉运动
    Servo_SetAngle(-45);    // 180度舵机-45度
    Servo2_SetAngle(90);    // 270度舵机90度
    HAL_Delay(1000);
    
    Servo_SetAngle(45);     // 180度舵机45度
    Servo2_SetAngle(225);   // 270度舵机225度
    HAL_Delay(1000);
    
    // 回到中位
    Servo_SetAngle(0);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机速度测试
 */
void Test_Servo270_SpeedTest(void) {
    // 快速运动测试
    Servo2_SetAngle(0);
    HAL_Delay(100);
    Servo2_SetAngle(270);
    HAL_Delay(100);
    Servo2_SetAngle(135);
    HAL_Delay(100);
    
    // 慢速运动测试
    Servo2_SetAngle(45);
    HAL_Delay(1000);
    Servo2_SetAngle(225);
    HAL_Delay(1000);
    Servo2_SetAngle(135);
    HAL_Delay(1000);
}

/**
 * @brief 270度舵机主测试函数
 */
void Servo270_RunAllTests(void) {
    Test_Servo270_BasicAngleControl();
    HAL_Delay(2000);
    
    Test_Servo270_ContinuousSweep();
    HAL_Delay(2000);
    
    Test_Servo270_PrecisePulseControl();
    HAL_Delay(2000);
    
    Test_Servo270_SegmentTest();
    HAL_Delay(2000);
    
    Test_Servo270_FineTuning();
    HAL_Delay(2000);
    
    Test_Mixed_ServoCoordination();
    HAL_Delay(2000);
    
    Test_Servo270_SpeedTest();
    HAL_Delay(2000);
}

/**
 * @brief 简单的270度舵机测试(用于main.c)
 */
void Servo270_SimpleTest(void) {
    // 简单的270度舵机测试序列
    Servo2_SetAngle(0);     // 0度
    HAL_Delay(1000);
    Servo2_SetAngle(90);    // 90度
    HAL_Delay(1000);
    Servo2_SetAngle(180);   // 180度
    HAL_Delay(1000);
    Servo2_SetAngle(270);   // 270度
    HAL_Delay(1000);
    Servo2_SetAngle(135);   // 回中位
    HAL_Delay(1000);
}
